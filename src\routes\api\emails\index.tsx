import { APIEvent } from "@solidjs/start/server";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { sendEmail } from "~/services/http-services/email-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function POST({ request, params }: APIEvent) {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    return makeJsonResponse(
      {
        success: false,
        message: "Please login to send email",
      },
      401,
    );
  }

  // Get POST data.
  const jsonBody = await request.json();

  const subject = jsonBody.subject;
  const body = jsonBody.body;
  const from = jsonBody.from;
  const to = jsonBody.to;
  const replyTo = jsonBody.replyTo;
  const cc = jsonBody.cc;
  const bcc = jsonBody.bcc;
  const button = jsonBody.button;

  const bodyContent = `
    <article>
      ${body}
    </article>

    <p>
      <a href="${button.url}" target="_blank" rel="noopener noreferrer" style="background-color: #3498db; border: 1px solid #3498db; border-radius: 4px; color: #ffffff; display: inline-block; font-family: Helvetica, Arial, sans-serif; font-size: 14px; font-weight: bold; line-height: 40px; text-align: center; text-decoration: none; width: 200px; -webkit-text-size-adjust: none; mso-hide: all;">${button.text}</a>
    </p>
  `;

  const response = await sendEmail({
    from: {
      name: from.name,
      email: from.email,
    },
    to: {
      name: to.name,
      email: to.email,
    },
    replyTo: {
      name: replyTo.name,
      email: replyTo.email,
    },
    cc: cc,
    bcc: bcc,
    subject: subject,
    body: bodyContent,
    button: button,
  });

  return makeJsonResponse(response);
}
