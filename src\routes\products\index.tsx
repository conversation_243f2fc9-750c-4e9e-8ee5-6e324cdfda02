import {
	A,
	RouteDefinition,
	createAsync,
	query,
	redirect,
	useSearchParams,
} from "@solidjs/router";
import { ChevronDown, ChevronUp, Search } from "lucide-solid";
import { Show, createSignal } from "solid-js";
import { getRequestURL } from "vinxi/http";
import LoadingSpinner from "~/components/LoadingSpinner";
import ProductListWithLoadMore from "~/components/products/ProductListWithLoadMore";
import { AppUrlData } from "~/types/misc";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { fetchProducts } from "~/services/http-services/product-service";

const getPageData = query(async () => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const urlObject = getRequestURL();
	const searchParams = urlObject.searchParams;
	const baseUrl = urlObject.origin;

	const urls: AppUrlData = {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		currentUrl: urlObject.href,
	};

	const keyword = searchParams.get("keyword");

	const productListResponse = await fetchProducts({
		sortDesc: false,
		keyword: keyword ?? undefined,
	});

	return {
		urls: urls,
		productListResponse: productListResponse,
	};
}, "salesOrderListPageData");

export const route = {
	preload: () => getPageData(),
} satisfies RouteDefinition;

export default function ProductListPage() {
	const pageData = createAsync(() => getPageData(), { deferStream: true });
	const [lowToHigh, setLowToHigh] = createSignal(false);

	let searchFieldRef: HTMLInputElement | undefined;
	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<div>
			<header class="fixed w-full bg-white px-[1rem] pt-[1rem] pb-[1rem] shadow-md md:px-0 md:pb-0">
				<div class="md:container md:flex">
					<div>
						<h4 class="text-2xl font-bold text-red-700">Products</h4>
						<nav class="mt-1 mb-1 flex items-center" aria-label="Breadcrumb">
							<ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
								<li class="inline-flex items-center">
									<A
										href="/"
										class="hover:text-primary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
									>
										Dashboard
									</A>
								</li>
								<li>
									<div class="flex items-center">
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>
										<span class="ms-1 text-sm text-gray-700 md:ms-2 dark:text-gray-400 dark:hover:text-white">
											Products
										</span>
									</div>
								</li>
							</ol>
						</nav>
					</div>

					<div class="my-auto ml-auto md:w-1/3">
						<div class="flex items-center rounded-md bg-gray-200 p-2">
							<Search class="text-gray-500" size={16} />
							<input
								ref={searchFieldRef}
								type="search"
								value={searchParams.keyword ?? ""}
								class="ml-2 w-full bg-transparent text-sm font-light outline-hidden"
								placeholder="Search by order or customer"
							/>
						</div>
					</div>
				</div>
				<div class="container hidden py-2 text-xs font-bold text-black/75 md:flex">
					<div class="w-2/6">Product Name</div>
					<div class="w-1/6">Category</div>
					<div class="w-1/6">SKU</div>
					<div class="w-1/6">
						<button
							class="flex items-center"
							onClick={() => setLowToHigh(!lowToHigh())}
						>
							Quantity
							<Show when={lowToHigh()} fallback={<ChevronDown class="ml-2" />}>
								<ChevronUp class="ml-2" />
							</Show>
						</button>
					</div>
					<div class="w-1/6 text-right">FOB $ Semarang</div>
				</div>
			</header>
			<Show
				when={pageData()?.urls && pageData()?.productListResponse}
				fallback={<LoadingSpinner class="pt-32" />}
			>
				<ProductListWithLoadMore
					baseApiUrl={pageData()?.urls?.baseApiUrl ?? ""}
					product={pageData()?.productListResponse?.data ?? []}
					searchField={searchFieldRef}
				/>
			</Show>
			<footer class="site-footer pt-12"></footer>
		</div>
	);
}
