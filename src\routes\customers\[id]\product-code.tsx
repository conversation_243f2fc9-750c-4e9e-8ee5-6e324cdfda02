import {
	A,
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { Search } from "lucide-solid";
import { Show } from "solid-js";
import { getRequestURL } from "vinxi/http";
import LoadingSpinner from "~/components/LoadingSpinner";
import CustomProductDataListWithLoadMore from "~/components/custom-product-data/CustomProductDataListWithLoadMore";
import { AppUrlData } from "~/types/misc";
import { getAuthData } from "~/services/auth-services/supabase-auth-service";
import { fetchCustomProductData } from "~/services/http-services/custom-product-data-service";
import { findCustomer } from "~/services/http-services/customer-service";

const getPageData = query(async (customerId: string) => {
	"use server";

	const authDataResponse = await getAuthData();

	if (!authDataResponse.success) {
		throw redirect("/login/");
	}

	const urlObject = getRequestURL();
	const baseUrl = urlObject.origin;

	const urls: AppUrlData = {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		apiUrl: `${baseUrl}/api/custom-product-data`,
		currentUrl: urlObject.href,
	};

	const customerResponse = await findCustomer(customerId);

	function fetchCustomProductDataListResponse() {
		if (customerId === "731e777d-8ef1-4d13-a77c-54a8842dcdc7") {
			return fetchCustomProductData({
				customerId: "1caa0847-b862-44a6-ab7f-a605ea8b67da",
				page: 0,
			});
		} else {
			return fetchCustomProductData({
				customerId: customerId,
				page: 0,
			});
		}
	}

	const customProductDataListResponse =
		await fetchCustomProductDataListResponse();

	// console.log("path", urlObject.pathname);
	// console.log("customerId:", customerId);

	return {
		urls: urls,
		profile: authDataResponse?.data,
		customerResponse,
		customProductDataListResponse,
	};
}, "productCodePageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function CustomerDetailPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	let searchFieldRef: HTMLInputElement | undefined;

	return (
		<>
			<header class="fixed w-full bg-white px-[1rem] pt-[1rem] pb-[1rem] shadow-md md:px-0 md:pb-0">
				<div class="md:container md:flex">
					<div>
						<h4 class="text-secondary text-2xl font-bold">
							<Show
								when={pageData()?.customerResponse.data}
								fallback="Customer's"
							>
								{pageData()?.customerResponse.data?.name ?? "Customers"}'s{" "}
								<span class="text-gray-600">products</span>
							</Show>
						</h4>
						<nav class="mt-1 mb-1 flex items-center" aria-label="Breadcrumb">
							<ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
								<li class="inline-flex items-center">
									<A
										href="/"
										class="hover:text-secondary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
									>
										Dashboard
									</A>
								</li>
								<li>
									<div class="flex items-center">
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>
										<A
											href="/customers/"
											class="hover:text-secondary ms-1 text-sm text-gray-700 md:ms-2 dark:text-gray-400 dark:hover:text-white"
										>
											Customers
										</A>
									</div>
								</li>
								<li>
									<div class="flex items-center">
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>

										<Show
											when={pageData()?.customerResponse.data}
											fallback="Customer"
										>
											<span class="ms-1 text-sm text-gray-700 md:ms-2 dark:text-gray-400 dark:hover:text-white">
												{pageData()?.customerResponse.data?.name ?? "Customer"}
											</span>
										</Show>
									</div>
								</li>
							</ol>
						</nav>
					</div>

					<div class="my-auto ml-auto md:w-1/3">
						<div class="flex items-center rounded-md bg-gray-200 p-2">
							<Search class="text-gray-500" size={16} />
							<input
								ref={searchFieldRef}
								type="text"
								class="ml-2 w-full bg-transparent text-sm font-light outline-hidden"
								placeholder="Search product"
							/>
						</div>
					</div>
				</div>
				<div class="container hidden py-2 text-xs font-bold text-black/75 md:flex">
					<div class="w-4/12 text-left">Product</div>
					<div class="w-2/12 pl-2 text-left">SKU</div>
					<div class="w-2/12 text-left">Customer product </div>
					<div class="w-3/12 text-left">Customer product name</div>
					<div class="w-1/12 text-left"></div>
				</div>
			</header>

			<Show when={pageData()} fallback={<LoadingSpinner class="pt-32" />}>
				<CustomProductDataListWithLoadMore
					apiUrl={pageData()?.urls.apiUrl ?? ""}
					baseApiUrl={pageData()?.urls.baseApiUrl ?? ""}
					profile={pageData()?.profile ?? undefined}
					customer={pageData()?.customerResponse.data ?? undefined}
					dataList={pageData()?.customProductDataListResponse.data ?? []}
					searchField={searchFieldRef}
				/>
			</Show>

			<footer class="site-footer pt-12"></footer>
		</>
	);
}
