import { Show, batch, createSignal } from "solid-js";
import { clientSendEmail } from "~/services/client-services/email-client-service";
import { SalesOrderData } from "~/types/dto";
import { EmailConfig } from "~/configs/app-config";
import { CheckCircle2, X } from "lucide-solid";
import { ucFirst } from "~/utils/string-util";
import { RouteDefinition, createAsync } from "@solidjs/router";
import { getAppUrlData } from "~/etc/data-queries";

export const route = {
	preload: () => getAppUrlData(),
} satisfies RouteDefinition;

interface SalesEmailFormProps {
	formName: string;
	id: string;
	form: string;
	salesOrder: SalesOrderData;
	onSend: () => void;
	button?: {
		text: string;
		url: string;
	};
	onCancel: () => void;
	onPreview?: () => void;
}

type Notice = {
	message: string;
	type: "success" | "error";
};

export default function SalesEmailForm(props: SalesEmailFormProps) {
	const appUrlData = createAsync(() => getAppUrlData(), { deferStream: true });

	const [replyToEmail, setReplyToEmail] = createSignal<string>(
		EmailConfig.replyToEmail,
	);

	const [replyToName, setReplyToName] = createSignal<string>(
		EmailConfig.replyToName,
	);

	const [toEmail, setToEmail] = createSignal<string>(
		props.salesOrder.customer?.email ?? "",
		// "<EMAIL>",
	);

	const [toName, setName] = createSignal<string>(
		props.salesOrder.customer?.name ?? "",
	);

	const [cc, setCc] = createSignal<string>("");
	const [bcc, setBcc] = createSignal<string>("");
	const [subject, setSubject] = createSignal<string>("");
	const [message, setMessage] = createSignal<string>("");

	const [notice, setNotice] = createSignal<Notice | undefined>();

	function handleReplyToEmailChange(value: string) {
		setReplyToEmail(value);
	}

	function handleReplyToNameChange(value: string) {
		setReplyToName(value);
	}

	function handleToEmailChange(value: string) {
		setToEmail(value);
	}

	function handleToNameChange(value: string) {
		setName(value);
	}

	function handleCcChange(value: string) {
		setCc(value);
	}

	function handleBccChange(value: string) {
		setBcc(value);
	}

	function handleSubjectChange(value: string) {
		setSubject(value);
	}

	function handleMessageChange(value: string) {
		setMessage(value);
	}

	const [isSubmitting, setIsSubmitting] = createSignal<boolean>(false);

	const randomStr = Math.random().toString(36).substring(10);

	async function handleSubmit(e: Event) {
		e.preventDefault();

		batch(() => {
			setIsSubmitting(true);
			setNotice(undefined);
		});

		const response = await clientSendEmail({
			url:
				appUrlData() && appUrlData()?.baseApiUrl
					? `${appUrlData()?.baseApiUrl}/emails`
					: "/api/emails",
			data: {
				from: {
					email: EmailConfig.senderEmail,
					name: replyToName(),
				},
				to: {
					email: toEmail(),
					name: toName(),
				},
				replyTo: {
					email: replyToEmail(),
					name: replyToName(),
				},
				cc: cc(),
				bcc: bcc(),
				subject: subject(),
				body: message(),
				button: {
					text: props.button?.text ?? `View your ${props.form}`,
					url: `https://multay.one/sales-orders/${props.id}/${props.form}/?c=${props.salesOrder.customerId}&r=${randomStr}`,
				},
			},
		});

		if (!response.success) {
			batch(() => {
				setIsSubmitting(false);
				setNotice({
					message: response.message,
					type: "error",
				});
			});

			return;
		}

		batch(() => {
			setIsSubmitting(false);
			setNotice({
				message: response.message,
				type: "success",
			});

			// props.onCancel();
		});
	}

	return (
		<Show
			when={notice()}
			fallback={
				<section class="fixed top-0 left-0 z-10 flex h-full w-full items-center justify-center bg-black/50 filter backdrop-blur-xs">
					<form
						method="post"
						class="font-base rounded-lg bg-[#F4F4F4] py-2 shadow-lg"
						onSubmit={handleSubmit}
					>
						<h3 class="mt-2 ml-2 text-[16px] font-semibold">
							Send {ucFirst(props.form)}
						</h3>
						<div class="mt-4 flex items-center px-2">
							<p class="mr-2">Reply to email</p>
							<input
								type="text"
								class="ml-auto w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
								value={EmailConfig.replyToEmail}
								onInput={(e) => handleReplyToEmailChange(e.currentTarget.value)}
							/>
						</div>
						<div class="mt-2 flex items-center border-b px-2 pb-2">
							<p class="mr-2">Reply to Name</p>
							<input
								type="text"
								class="ml-auto w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
								name="reply-to"
								id="reply-to"
								value={EmailConfig.replyToName}
								onChange={(e) => {
									handleReplyToNameChange(e.currentTarget.value);
								}}
							/>
						</div>
						<div class="mt-2 border-b px-2 pb-2">
							<div class="mt-2 flex items-center">
								<p class="mr-2">To email</p>
								<input
									type="text"
									class="ml-auto w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
									name="to"
									id="to"
									value={props.salesOrder.customer?.email ?? ""}
									// value="<EMAIL>"
									onChange={(e) => {
										handleToEmailChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mt-2 flex items-center">
								<p class="mr-2">To Name</p>
								<input
									type="text"
									class="ml-auto w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
									name="to_name"
									id="to_name"
									value={props.salesOrder.customer?.name ?? ""}
									onChange={(e) => {
										handleToNameChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mt-2 flex items-center">
								<p class="mr-2">Cc</p>
								<input
									type="text"
									class="ml-auto w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
									name="cc"
									id="cc"
									onChange={(e) => {
										handleCcChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mt-2 flex items-center">
								<p class="mr-2">Bcc</p>
								<input
									type="text"
									class="ml-auto w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
									name="bcc"
									id="bcc"
									onChange={(e) => {
										handleBccChange(e.currentTarget.value);
									}}
								/>
							</div>
						</div>
						<div class="mt-2 px-2">
							<div class="mt-2 flex items-center">
								<p class="mr-2">Subject</p>
								<input
									type="text"
									class="ml-auto w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
									name="subject"
									id="subject"
									onChange={(e) => {
										handleSubjectChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="items-top mt-2 flex">
								<p class="mr-2">Message</p>
								<textarea
									class="ml-auto min-h-20 w-60 rounded-lg border border-gray-300/50 p-1 md:w-72 lg:w-80 xl:w-96"
									name="message"
									id="message"
									rows={3}
									onChange={(e) => {
										handleMessageChange(e.currentTarget.value);
									}}
								>{`Dear ${props.salesOrder.customer?.contactName}, below is your ${props.form} for order number ${props.salesOrder.orderNumber}`}</textarea>
							</div>
						</div>
						<div class="mt-2 flex justify-end pt-2 pr-2">
							<button
								type="button"
								class="rounded-sm border border-slate-800 px-3 py-1.5 font-medium"
								onClick={props.onCancel}
							>
								Cancel
							</button>
							<button
								type="submit"
								class="ml-2 rounded-sm bg-[#F1B246] px-3 py-1.5 font-medium"
							>
								Send
							</button>
						</div>
					</form>
				</section>
			}
		>
			<div class="fixed top-0 left-0 z-10 flex h-full w-full items-center justify-center bg-black/50">
				<div class="rounded-sm bg-white px-4 py-2">
					{/* close button right */}
					<div class="flex">
						<button class="mt-1 ml-auto" onClick={props.onCancel}>
							<X class="h-5 w-5" />
						</button>
					</div>
					{/* center icon */}
					<CheckCircle2 class="mx-auto h-20 w-20 text-green-500" />
					<h1 class="mt-5 text-center text-3xl font-bold"> Success </h1>
					<div class="mx-auto mt-3 mb-5 w-2/3 text-center">
						<i class="text-sm font-medium">
							<strong>{props.formName}</strong> form has been sent to{" "}
							<strong>{toEmail()}</strong>
						</i>
					</div>
				</div>
			</div>
		</Show>
	);
}
