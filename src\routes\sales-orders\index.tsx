import { fetchSalesOrders } from "~/services/http-services/sales-order-service";
import { Show } from "solid-js";
import LoadingSpinner from "~/components/LoadingSpinner";
import SalesOrderListWithLoadMore from "~/components/sales-orders/SalesOrderListWithLoadMore";
import {
	A,
	RouteDefinition,
	createAsync,
	query,
	redirect,
	useSearchParams,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { Search } from "lucide-solid";
import { getRequestURL } from "vinxi/http";
import { AppUrlData } from "~/types/misc";

const getPageData = query(async () => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const urlObject = getRequestURL();
	const baseUrl = urlObject.origin;
	const searchParams = urlObject.searchParams;

	const urls: AppUrlData = {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		currentUrl: urlObject.href,
	};

	const keyword = searchParams.get("keyword");

	const salesOrderListResponse = await fetchSalesOrders({
		sortDesc: true,
		keyword: keyword ? `${keyword}` : undefined,
		filters: {
			isActive: true,
		},
	});

	const notFound = salesOrderListResponse?.data?.length === 0;

	return {
		urls,
		salesOrderListResponse,
		notFound,
	};
}, "salesOrderListPageData");

export const route = {
	preload: () => getPageData(),
} satisfies RouteDefinition;

export default function SalesOrderListPage() {
	const pageData = createAsync(() => getPageData(), { deferStream: true });

	let searchFieldRef: HTMLInputElement | undefined;

	const [searchParams, setSearchParams] = useSearchParams();

	return (
		<>
			<header class="fixed z-10 w-full bg-white px-[1rem] pt-[1rem] pb-[1rem] shadow-md md:px-0 md:pb-0">
				<div class="md:container md:flex">
					<div>
						<h4 class="text-primary text-2xl font-bold">Sales orders</h4>
						<nav class="mt-1 mb-1 flex items-center" aria-label="Breadcrumb">
							<ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
								<li class="inline-flex items-center">
									<A
										href="/"
										class="hover:text-primary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
									>
										Dashboard
									</A>
								</li>
								<li>
									<div class="flex items-center">
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>
										<span class="ms-1 text-sm text-gray-700 md:ms-2 dark:text-gray-400 dark:hover:text-white">
											Sales orders
										</span>
									</div>
								</li>
							</ol>
						</nav>
					</div>

					<div class="my-auto ml-auto md:w-1/3">
						<div class="flex items-center rounded-md bg-gray-200 p-2">
							<Search class="text-gray-500" size={16} />
							<input
								ref={searchFieldRef}
								type="search"
								value={searchParams.keyword ?? ""}
								class="ml-2 w-full bg-transparent text-sm font-light outline-hidden"
								placeholder="Search by order or customer"
							/>
						</div>
					</div>
				</div>
				<div class="container hidden py-2 text-xs font-bold text-black/75 md:flex">
					<div class="w-[20%]">Order number</div>
					<div class="w-[22%]">Customer</div>
					<div class="w-[10%]">Order date</div>
					<div class="w-[10%]">Requested ship date</div>
					<div class="w-[20%]">Shipping address</div>
					<div class="w-[9%]">Total</div>
					<div class="w-[9%] text-right">Balance</div>
				</div>
			</header>

			<Show
				when={
					pageData() && pageData()?.salesOrderListResponse && pageData()?.urls
				}
				fallback={<LoadingSpinner class="pt-32" />}
			>
				<SalesOrderListWithLoadMore
					baseApiUrl={pageData()?.urls?.baseApiUrl ?? ""}
					salesOrders={pageData()?.salesOrderListResponse?.data ?? []}
					searchField={searchFieldRef}
				/>
			</Show>

			<div
				class={pageData()?.notFound ? "text-center text-black/50" : "hidden"}
			>
				<h1 class="text-xl font-bold">"{searchParams.keyword}" not found</h1>
				<p class="mt-2">please try another keyword</p>
			</div>

			<footer class="site-footer pt-12"></footer>
		</>
	);
}
