import currency from "currency.js";
import dayjs from "dayjs";
import { numericToNumber } from "./number-util";

export function formatCurrency(props: {
	amount: string | number;
	symbol?: string;
	thousandsSeparator?: string;
	decimalSeparator?: string;
	decimalDigits?: number;
}): string {
	return currency(
		typeof props.amount === "string"
			? numericToNumber(props.amount)
			: props.amount,
		{
			symbol: props.symbol ?? "$",
			separator: props.thousandsSeparator ?? ",",
			decimal: props.decimalSeparator ?? ".",
			precision: props.decimalDigits ?? 2,
		},
	).format();
}

export function formatSize(size: string | number): string {
	const $number = Number(size);

	return $number.toLocaleString("en-US", {
		minimumFractionDigits: 0,
		maximumFractionDigits: 3,
	});
}

export function formatDimension(size: string | number): string {
	const $number = Number(size);

	return $number.toLocaleString("en-US", {
		minimumFractionDigits: 3,
		maximumFractionDigits: 3,
	});
}

export function formatCBM(size: string | number): string {
	const $number = Number(size);

	return $number.toLocaleString("en-US", {
		minimumFractionDigits: 3,
		maximumFractionDigits: 3,
	});
}

export function formatQuantity(quantity: string | number): string {
	const $number = Number(quantity);

	return $number.toLocaleString("en-US", {
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	});
}

export function formatDiscount(discount: string | number): string {
	const $number = Number(discount);

	return $number.toLocaleString("en-US", {
		minimumFractionDigits: 0,
		maximumFractionDigits: 3,
	});
}

export function formatWeight(weight: string | number): string {
	const $number = Number(weight);

	return $number.toLocaleString("en-US", {
		minimumFractionDigits: 3,
		maximumFractionDigits: 3,
	});
}

export function formatDate(date: string): string {
	const dateObj = dayjs(date);
	return dateObj.format("MMM DD, YYYY");
}

export function formatStringForXlsx(value: string): string {
	return value.replace(/\+/g, " ");
}
