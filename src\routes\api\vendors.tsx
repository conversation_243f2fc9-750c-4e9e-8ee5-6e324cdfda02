import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { fetchVendors } from "~/services/http-services/vendor-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to load vendors",
			},
			401,
		);
	}

	const urlObject = new URL(request.url);
	const searchParams = urlObject.searchParams;

	const includeCount = searchParams.get("includeCount");
	const count = searchParams.get("count");
	const after = searchParams.get("after");
	const before = searchParams.get("before");
	const start = searchParams.get("start");
	const skip = searchParams.get("skip");
	const sort = searchParams.get("sort");
	const sortDesc = searchParams.get("sortDesc");
	const keyword = searchParams.get("keyword");
	const filters = searchParams.get("filters");

	const response = await fetchVendors({
		includeCount: includeCount === "true",
		count: count ? Number(count) : undefined,
		after: after ?? undefined,
		before: before ?? undefined,
		start: start ?? undefined,
		skip: skip ? Number(skip) : undefined,
		sort: sort ?? undefined,
		sortDesc: sortDesc === "true",
		keyword: keyword ?? undefined,
		filters: filters ? JSON.parse(filters) : undefined,
	});

	return makeJsonResponse(response);
}
