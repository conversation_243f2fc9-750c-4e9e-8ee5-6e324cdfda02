import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { getAuthData, isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import {
  createCustomProductData,
  fetchCustomProductData,
} from "~/services/http-services/custom-product-data-service";
import { makeJsonResponse } from "~/utils/http-util";
import { isAdmin } from "~/utils/user-util";

export async function GET({ request, params }: APIEvent) {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    return makeJsonResponse(
      {
        success: false,
        message: "Please login to load custom product data",
      },
      401,
    );
  }

  const urlObject = new URL(request.url);
  const searchParams = urlObject.searchParams;

  const customerId = searchParams.get("customer_id");
  const page = searchParams.get("page");
  const perPage = searchParams.get("per_page");
  const keyword = searchParams.get("keyword");

  const response = await fetchCustomProductData({
    customerId: customerId ?? undefined,
    page: page ? Number(page) : undefined,
    perPage: perPage ? Number(perPage) : undefined,
    keyword: keyword ?? undefined,
  });

  return makeJsonResponse(response);
}

export async function POST({ request, params }: APIEvent) {
  const authDataResponse = await getAuthData();

  if (!authDataResponse || !authDataResponse.data) {
    return makeJsonResponse(
      {
        success: false,
        message: "Please login to create custom product data",
      },
      401,
    );
  }

  if (!isAdmin(authDataResponse.data)) {
    return makeJsonResponse(
      {
        success: false,
        message: "You are not authorized to create custom product data",
      },
      403,
    );
  }

  // Get POST data.
  const body = await request.json();

  const customerId = body.customerId;
  const customerName = body.customerName;
  const productId = body.productId;
  const productName = body.productName;
  const productSku = body.productSku;
  const customerProductCode = body.customerProductCode;
  const customerProductName = body.customerProductName;

  const response = await createCustomProductData({
    id: 0,
    customerId: customerId,
    customerName: customerName,
    productId: productId,
    productName: productName,
    customerProductCode: customerProductCode,
    customerProductName: customerProductName,
    productSku: productSku,
  });

  return makeJsonResponse(response);
}
