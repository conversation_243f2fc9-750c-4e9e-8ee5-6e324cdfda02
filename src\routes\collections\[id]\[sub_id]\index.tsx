import { Show } from "solid-js";
import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { fetchCollectionProducts } from "~/services/http-services/page-service";
import LoadingSpinner from "~/components/LoadingSpinner";
import {
	groupProductsByCategory,
	recursivelyFindMatchingCategory,
} from "~/utils/category-util";
import CollectionPriceListTable from "~/components/collections/CollectionPriceListTable";
import {
	GroupedProductsByCategoryData,
	ProductData,
	StructuredCategoryData,
} from "~/types/dto";
import { dbFindCollectionCategory } from "~/services/db-services/collection-db-service";

const getPageData = query(
	async (collectionId: string, subCollectionId: string) => {
		"use server";

		const isLoggedIn = await isAuthenticated();

		if (!isLoggedIn) {
			throw redirect("/login/");
		}

		let errorMessage: string | undefined = undefined;

		const collection = await dbFindCollectionCategory(collectionId);

		const subCollection = collection
			? recursivelyFindMatchingCategory(subCollectionId, collection)
			: undefined;

		const products = await fetchCollectionProducts({
			existingProducts: [],
			perPage: 100,
			collectionId: subCollectionId,
		});

		return {
			errorMessage,
			collection,
			subCollection,
			products,
		};
	},
	"collectionSubIdPageData",
);

export const route = {
	preload: ({ params }) => getPageData(params.id, params.sub_id),
} satisfies RouteDefinition;

export default function CollectionSubIdPage(props: RouteSectionProps) {
	const pageData = createAsync(
		() => getPageData(props.params.id, props.params.sub_id),
		{
			deferStream: true,
		},
	);

	function categoryHasChildren(category: StructuredCategoryData) {
		return category.children && category.children.length > 0;
	}

	function makeGroupedProducts(
		category: StructuredCategoryData,
		products: ProductData[],
	): GroupedProductsByCategoryData[] {
		return categoryHasChildren(category)
			? groupProductsByCategory(category, products)
			: [
					{
						categoryId: category.categoryId,
						name: category.name,
						products: products,
					},
				];
	}

	return (
		<>
			<Show when={!pageData()}>
				<div class="relative flex h-full w-full items-center justify-center">
					<LoadingSpinner class="mt-14" />
				</div>
			</Show>

			<Show when={pageData()}>
				<Show
					when={!pageData()?.errorMessage}
					fallback={pageData()?.errorMessage}
				>
					<div class="relative min-h-screen w-full bg-cover bg-fixed">
						<div class="fixed h-screen w-screen bg-cover print:hidden"></div>

						<div class="relative mx-auto w-[1280px] max-w-[1500px] px-6 py-14 lg:px-14 2xl:w-auto print:w-auto print:min-w-auto print:px-0 print:py-0 print:pt-10">
							<h2 class="relative flex items-center justify-between">
								<span class="font-display relative block w-10/12 text-center text-2xl font-bold text-wrap text-[#656565]">
									<Show when={pageData()?.subCollection}>
										{pageData()?.subCollection?.name}

										<Show when={pageData()?.collection}>
											{` ${pageData()?.collection?.name}`}
										</Show>
									</Show>
									{` Price List FOB Semarang`}
								</span>

								<span class="relative block w-2/12 text-center">
									<img
										src="/images/legacy-home-indonesia-logo.png"
										alt="Logo"
										class="mx-auto h-20 w-auto max-w-full"
									/>
								</span>
							</h2>

							<CollectionPriceListTable
								category={pageData()?.subCollection!}
								groupedProducts={makeGroupedProducts(
									pageData()?.subCollection!,
									pageData()?.products ?? [],
								)}
							/>
						</div>
					</div>
				</Show>
			</Show>
		</>
	);
}
