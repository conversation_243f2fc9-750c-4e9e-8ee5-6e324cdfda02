import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { For, Show } from "solid-js";
import PurchaseActionButtons from "~/components/purchase-order/PurchaseActionButtons";
import PurchaseOrderTable from "~/components/tables/PurchaseOrderTable";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { fetchPurchaseOrders } from "~/services/http-services/purchase-order-service";
import { findVendor } from "~/services/http-services/vendor-service";
import { formatDate } from "~/utils/formatting-util";

const getPageData = query(async (vendorId: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	const vendorResponse = await findVendor(vendorId);

	const purchaseOrderListResponse = await fetchPurchaseOrders({
		count: 100,
		sortDesc: true,
		filters: {
			isActive: true,
			vendorId: vendorId,
			inventoryStatus: ["unfulfilled", "started"],
		},
	});

	return {
		vendor: vendorResponse.data,
		purchaseOrderList: purchaseOrderListResponse.data,
	};
}, "unfulfilledPoPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function UnfulfilledPoPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	function formatShipDate(shipDate: string): string {
		if (!shipDate) return "";

		const shipDateSplits = shipDate.split("T");
		const shipDateSimple = shipDateSplits[0];

		return formatDate(shipDateSimple);
	}

	return (
		<div class="w-[1800px] px-4 py-12 lg:w-auto xl:px-20 print:w-auto print:px-0 print:py-0">
			<Show when={pageData()} fallback="Loading data...">
				<Show
					when={pageData()?.purchaseOrderList}
					fallback="Failed to load purchase order"
				>
					<Show when={pageData()?.vendor}>
						<h1 class="text-center text-3xl font-bold">
							{pageData()?.vendor?.name}
						</h1>
					</Show>
					<h2 class="text-md mb-5 text-center font-semibold text-neutral-600">
						Unfulfilled Purchase Orders
					</h2>

					<For each={pageData()?.purchaseOrderList}>
						{(purchaseOrder) => {
							return (
								<table class="relative mt-12 w-full rounded-sm border border-dashed border-neutral-300 bg-neutral-50 print:mt-2">
									<thead>
										<tr>
											<th class="relative w-full px-4 pt-4">
												<div class="flex border-t border-l border-black bg-gray-100 text-sm print:text-[8px]">
													<div class="w-1/2 border-r border-black py-1 text-center print:py-0">
														Order Number
													</div>
													<div class="w-1/2 border-r border-black py-1 text-center print:py-0">
														Requested Ship Date
													</div>
												</div>
												<div class="flex border-y border-l border-black text-sm opacity-75 print:text-[9px] print:leading-[10px] print:font-light">
													<div class="flex w-1/2 items-center border-r border-black py-1 text-center">
														<p class="m-auto">{purchaseOrder.orderNumber}</p>
													</div>
													<div class="flex w-1/2 items-center border-r border-black py-1 text-center">
														<p
															class="m-auto"
															data-ship-date={formatShipDate(
																purchaseOrder?.requestShipDate ?? "",
															)}
														>
															{formatShipDate(
																purchaseOrder?.requestShipDate ?? "",
															)}
														</p>
													</div>
												</div>
											</th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td class="relative w-full px-4 pb-4">
												<PurchaseOrderTable
													purchaseOrder={purchaseOrder}
													showOnlyUncompletedProducts={true}
													removeActionButtons={true}
												/>
											</td>
										</tr>
									</tbody>
								</table>
							);
						}}
					</For>

					<PurchaseActionButtons formName="Purchase Order" />
				</Show>
			</Show>
		</div>
	);
}
