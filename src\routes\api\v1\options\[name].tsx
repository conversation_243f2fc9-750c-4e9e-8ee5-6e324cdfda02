import { APIEvent } from "@solidjs/start/server";
import { validateBasicAuth } from "~/services/auth-services/basic-auth-service";
import { findOption } from "~/services/db-services/option-db-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
	"use server";

	// HTTP Basic Authorization check
	const authError = validateBasicAuth(request);

	if (authError) {
		return authError;
	}

	const name = params.name;

	if (!name) {
		return makeJsonResponse(
			{
				success: false,
				message: "Option name is required",
			},
			400,
		);
	}

	const option = await findOption(name);

	if (option === null) {
		return makeJsonResponse(
			{
				success: false,
				message: "Option not found",
			},
			404,
		);
	}

	return makeJsonResponse({
		success: true,
		message: "Option retrieved successfully",
		data: option,
	});
}
