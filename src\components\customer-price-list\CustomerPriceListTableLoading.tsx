import { For } from "solid-js";
import LoadingShimmer from "../LoadingShimmer";
import PriceListActionButtons from "../collections/PriceListActionButtons";

export default function CustomerPriceListTableLoading() {
	// Create mock rows for skeleton loading
	const mockRows = Array.from({ length: 8 }, (_, i) => i);

	const thSharedClass = "p-1 font-bold text-center uppercase";
	const tdSharedClass = "p-1 print:p-0.5 xl:p-1.5";

	return (
		<div class="relative mx-auto w-[1800px] max-w-[1800px] px-6 py-14 lg:px-14 2xl:w-auto print:w-auto print:min-w-auto print:px-0 print:py-0 print:pt-10">
			<h2 class="relative flex items-center justify-between">
				<span class="font-display relative block w-10/12 text-center text-3xl font-bold text-wrap text-[#656565]">
					Price List
					<br />
					<LoadingShimmer class="mx-auto mt-3 h-9 w-48" />
				</span>

				<span class="relative block w-2/12 text-center">
					<img
						src="/images/legacy-home-indonesia-logo.png"
						alt="Logo"
						class="mx-auto h-20 w-auto max-w-full"
					/>
				</span>
			</h2>

			<div class="text-md mt-10 text-stone-800 print:text-[10px]">
				<table class="font-table table w-full table-fixed">
					<thead class="border-b border-stone-200 text-center uppercase">
						<tr class="w-full text-center print:text-sm">
							<th class={`${thSharedClass} w-[24%] px-1`} rowSpan={2}>
								Legacy Home Code
							</th>
							<th class={`${thSharedClass} w-[30%] px-1`} rowSpan={2}>
								Legacy Home Description
							</th>

							<th class={`${thSharedClass} w-[16%] px-3`} colspan={3}>
								Dimensions (cm)
							</th>

							<th class={`${thSharedClass} w-[8%]`} rowSpan={2}>
								M3
							</th>

							<th class={`${thSharedClass} w-[11%]`} rowSpan={2}>
								Legacy Home
								<br /> FOB Price USD
								<br />
								FEB 2024
							</th>

							<th class={`${thSharedClass} w-[11%]`} rowSpan={2}>
								Customer Existing
								<br />
								Price Dated
								<br />
								<span>-</span>
							</th>
						</tr>

						<tr class="w-full font-bold print:text-sm">
							<th class={`${thSharedClass} pl-3`}>
								H
							</th>
							<th class={`${thSharedClass} px-1.5`}>
								W
							</th>
							<th class={`${thSharedClass} pr-3`}>
								D
							</th>
						</tr>
					</thead>

					<tbody class="align-baseline">
						<For each={mockRows}>
							{() => (
								<tr class="group relative">
									<td class={`${tdSharedClass}`}>
										<LoadingShimmer class="h-10 w-full" />
									</td>

									<td class={`${tdSharedClass}`}>
										<LoadingShimmer class="h-10 w-full" />
									</td>

									<td class={`${tdSharedClass} text-center`}>
										<LoadingShimmer class="mx-auto h-5 w-12" />
									</td>

									<td class={`${tdSharedClass} text-center`}>
										<LoadingShimmer class="mx-auto h-5 w-12" />
									</td>

									<td class={`${tdSharedClass} text-center`}>
										<LoadingShimmer class="mx-auto h-5 w-12" />
									</td>

									<td class={`${tdSharedClass} text-center`}>
										<LoadingShimmer class="mx-auto h-5 w-16" />
									</td>

									<td class={`${tdSharedClass} text-center`}>
										<LoadingShimmer class="mx-auto h-5 w-16" />
									</td>

									<td class={`${tdSharedClass} text-center`}>
										<LoadingShimmer class="mx-auto h-5 w-16" />
									</td>
								</tr>
							)}
						</For>
					</tbody>
				</table>
			</div>
		</div>
	);
}
