import { basicAuthConfig } from "~/configs/server-config";
import { makeJsonResponse } from "~/utils/http-util";

/**
 * Validates HTTP Basic Authorization header against configured credentials
 * @param request - The incoming HTTP request
 * @returns Response object if authentication fails, null if authentication succeeds
 */
export function validateBasicAuth(request: Request): Response | null {
	"use server";

	const accessKey = basicAuthConfig().accessKey;
	const secretKey = basicAuthConfig().secretKey;

	// HTTP Basic Authorization check
	const authHeader = request.headers.get("Authorization");

	if (!authHeader || !authHeader.startsWith("Basic ")) {
		return makeJsonResponse(
			{
				success: false,
				message: "Missing or invalid Authorization header",
			},
			401,
		);
	}

	try {
		// Extract and decode the base64 credentials
		const base64Credentials = authHeader.substring("Basic ".length).trim();
		const credentials = Buffer.from(base64Credentials, 'base64').toString('utf-8');
		const [providedAccessKey, providedSecretKey] = credentials.split(":", 2);

		// Verify credentials exist and match
		if (!providedAccessKey || !providedSecretKey ||
			providedAccessKey !== accessKey || providedSecretKey !== secretKey) {
			return makeJsonResponse(
				{
					success: false,
					message: "Invalid credentials",
				},
				401,
			);
		}
	} catch (error) {
		return makeJsonResponse(
			{
				success: false,
				message: "Invalid Authorization header format",
			},
			401,
		);
	}

	// Authentication successful
	return null;
}
