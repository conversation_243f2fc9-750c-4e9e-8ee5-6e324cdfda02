import { RouteDefinition, createAsync, query, redirect } from "@solidjs/router";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import LoginForm from "~/components/auth/LoginForm";

const getPageData = query(async () => {
	"use server";

	if (await isAuthenticated()) {
		throw redirect("/");
	}
}, "loginPageData");

export const route = {
	preload: () => getPageData(),
} satisfies RouteDefinition;

export default function LoginPage() {
	const loginPageData = createAsync(() => getPageData(), { deferStream: true });

	return <LoginForm />;
}
