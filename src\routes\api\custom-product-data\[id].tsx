import { APIEvent } from "@solidjs/start/server";
import { getAuthData, isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import {
	deleteCustomProductData,
	findCustomProductData,
	updateCustomProductData,
} from "~/services/http-services/custom-product-data-service";
import { makeJsonResponse } from "~/utils/http-util";
import { isAdmin } from "~/utils/user-util";

export async function DELETE({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to delete custom product data",
			},
			401,
		);
	}

	if (!isAdmin(authDataResponse.data)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to delete custom product data",
			},
			403,
		);
	}

	const id = params.id;

	const response = await deleteCustomProductData(Number(id));

	return makeJsonResponse(response);
}

export async function GET({ request, params }: APIEvent) {
	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to get custom product data",
			},
			401,
		);
	}

	const id = params.id;

	const response = await findCustomProductData(Number(id));

	return makeJsonResponse(response);
}

export async function PUT({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to update custom product data",
			},
			401,
		);
	}

	if (!isAdmin(authDataResponse.data)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to update custom product data",
			},
			403,
		);
	}

	// Get POST data.
	const body = await request.json();

	const customerId = body.customerId;
	const customerName = body.customerName;
	const productId = body.productId;
	const productName = body.productName;
	const productSku = body.productSku;
	const customerProductCode = body.customerProductCode;
	const customerProductName = body.customerProductName;

	const response = await updateCustomProductData({
		id: Number(body.id),
		customerId: customerId,
		customerName: customerName,
		productId: productId,
		productName: productName,
		customerProductCode: customerProductCode,
		customerProductName: customerProductName,
		productSku: productSku,
	});

	return makeJsonResponse(response);
}
