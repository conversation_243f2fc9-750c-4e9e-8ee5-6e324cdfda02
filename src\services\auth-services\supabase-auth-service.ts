import {
	AuthDataResponse,
	AuthResponse,
	LogOutResponse,
} from "~/types/response";
import { safeParse } from "valibot";
import { LoginFormSchema, RegisterFormSchema } from "~/data-schema/form-schema";
import { CustomResponse, redirect } from "@solidjs/router";
import {
	makeRegistrationData,
	supabaseUserToProfileData,
} from "~/utils/user-util";
import { getErrorMessage } from "~/utils/http-util";
import { getSupabaseServer } from "../http-services/supabase-service";

export async function register(formData: FormData): Promise<AuthResponse> {
	"use server";

	const registrationData = makeRegistrationData(formData);
	const registerValidation = safeParse(RegisterFormSchema, registrationData);

	if (!registerValidation.success) {
		return {
			success: false,
			issues: registerValidation.issues,
		};
	}

	try {
		const { data, error } = await getSupabaseServer().auth.signUp({
			email: registrationData.email,
			phone: registrationData.phone,
			password: registrationData.password,
			options: {
				data: {
					role: registrationData.role,
					firstName: registrationData.firstName,
					lastName: registrationData.lastName,
					inflowUserId: registrationData.inflowUserId,
					address: registrationData.address,
					avatarUrl: registrationData.avatarUrl,
				},
			},
		});

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		return {
			success: true,
			data: data,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function login(formData: FormData): Promise<AuthResponse> {
	"use server";

	const email = String(formData.get("email"));
	const password = String(formData.get("password"));
	const loginValidation = safeParse(LoginFormSchema, { email, password });

	if (!loginValidation.success) {
		return {
			success: false,
			message: "Invalid login",
			issues: loginValidation.issues,
		};
	}

	try {
		const {
			data: { user },
			error,
		} = await getSupabaseServer().auth.signInWithPassword({
			email: email,
			password: password,
		});

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		if (!user) {
			return {
				success: false,
				message: "User not found",
			};
		}

		return {
			success: true,
			message: "You've been logged in successfully",
			data: supabaseUserToProfileData(user),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function logout(): Promise<
	LogOutResponse | CustomResponse<never>
> {
	"use server";

	try {
		const { error } = await getSupabaseServer().auth.signOut();

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		return redirect("/login/");
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function isAuthenticated(): Promise<boolean> {
	"use server";

	try {
		const {
			data: { user },
			error,
		} = await getSupabaseServer().auth.getUser();

		if (error) {
			return false;
		}

		if (!user) {
			return false;
		}

		return true;
	} catch (e: unknown) {
		return false;
	}
}

export async function getAuthData(): Promise<AuthDataResponse> {
	"use server";

	try {
		const {
			data: { user },
			error,
		} = await getSupabaseServer().auth.getUser();

		if (error) {
			return {
				success: false,
				message: error.message,
			};
		}

		if (!user) {
			return {
				success: false,
				message: "User is not authenticated",
			};
		}

		return {
			success: true,
			message: "Authentication data retrieved",
			data: supabaseUserToProfileData(user),
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
