import { Show, batch, createSignal } from "solid-js";
import LoadingSpinner from "./LoadingSpinner";
import { clientSendEmail } from "~/services/client-services/email-client-service";
import { SalesOrderData } from "~/types/dto";
import { EmailConfig } from "~/configs/app-config";
import { CheckCircle2, X } from "lucide-solid";
import { ucFirst } from "~/utils/string-util";
import { RouteDefinition, createAsync } from "@solidjs/router";
import { getAppUrlData } from "~/etc/data-queries";

export const route = {
	preload: () => getAppUrlData(),
} satisfies RouteDefinition;

interface SendEmailFormProps {
	formName: string;
	id: string;
	form: string;
	salesOrder: SalesOrderData;
	onSend: () => void;
	button?: {
		text: string;
		url: string;
	};
	onCancel: () => void;
	onPreview?: () => void;
}

type Notice = {
	message: string;
	type: "success" | "error";
};

export default function SendEmailForm(props: SendEmailFormProps) {
	const appUrlData = createAsync(() => getAppUrlData(), { deferStream: true });

	const [replyToEmail, setReplyToEmail] = createSignal<string>(
		EmailConfig.replyToEmail,
	);

	const [replyToName, setReplyToName] = createSignal<string>(
		EmailConfig.replyToName,
	);

	const [toEmail, setToEmail] = createSignal<string>(
		props.salesOrder.customer?.email ?? "",
		// "<EMAIL>",
	);

	const [toName, setName] = createSignal<string>(
		props.salesOrder.customer?.name ?? "",
	);

	const [cc, setCc] = createSignal<string>("");
	const [bcc, setBcc] = createSignal<string>("");
	const [subject, setSubject] = createSignal<string>("");
	const [message, setMessage] = createSignal<string>("");

	const [notice, setNotice] = createSignal<Notice | undefined>();

	function handleReplyToEmailChange(value: string) {
		setReplyToEmail(value);
	}

	function handleReplyToNameChange(value: string) {
		setReplyToName(value);
	}

	function handleToEmailChange(value: string) {
		setToEmail(value);
	}

	function handleToNameChange(value: string) {
		setName(value);
	}

	function handleCcChange(value: string) {
		setCc(value);
	}

	function handleBccChange(value: string) {
		setBcc(value);
	}

	function handleSubjectChange(value: string) {
		setSubject(value);
	}

	function handleMessageChange(value: string) {
		setMessage(value);
	}

	const [isSubmitting, setIsSubmitting] = createSignal<boolean>(false);

	const randomStr = Math.random().toString(36).substring(10);

	async function handleSubmit(e: Event) {
		e.preventDefault();

		batch(() => {
			setIsSubmitting(true);
			setNotice(undefined);
		});

		const response = await clientSendEmail({
			url:
				appUrlData() && appUrlData()?.baseApiUrl
					? `${appUrlData()?.baseApiUrl}/emails`
					: "/api/emails",
			data: {
				from: {
					email: EmailConfig.senderEmail,
					name: replyToName(),
				},
				to: {
					email: toEmail(),
					name: toName(),
				},
				replyTo: {
					email: replyToEmail(),
					name: replyToName(),
				},
				cc: cc(),
				bcc: bcc(),
				subject: subject(),
				body: message(),
				button: {
					text: props.button?.text ?? `View your ${props.form}`,
					url: `https://multay.one/sales-orders/${props.id}/${props.form}/?c=${props.salesOrder.customerId}&r=${randomStr}`,
				},
			},
		});

		if (!response.success) {
			batch(() => {
				setIsSubmitting(false);
				setNotice({
					message: response.message,
					type: "error",
				});
			});

			return;
		}

		batch(() => {
			setIsSubmitting(false);
			setNotice({
				message: response.message,
				type: "success",
			});

			// props.onCancel();
		});
	}

	return (
		<Show
			when={notice()}
			fallback={
				<section class="fixed top-0 left-0 z-10 flex h-full w-full items-center justify-center bg-black/50">
					<form
						method="post"
						class="relative rounded-lg bg-white sm:w-1/2 lg:w-1/3"
						onSubmit={handleSubmit}
					>
						<h3 class="relative rounded-tl-lg rounded-tr-lg bg-gray-100 px-4 py-4 text-lg font-medium text-gray-900">
							Send {props.form}
							<button
								class="absolute right-4 cursor-pointer rounded-full bg-black px-2 py-2 text-sm text-white"
								onClick={props.onCancel}
							>
								<X />
							</button>
						</h3>
						<div class="px-4 py-4">
							<div class="mb-4">
								<label
									for="reply-to"
									class="block text-sm font-medium text-gray-700"
								>
									Reply to email
								</label>
								<input
									type="email"
									name="reply-to"
									id="reply-to"
									value={EmailConfig.replyToEmail}
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter reply to email"
									required
									onChange={(e) => {
										handleReplyToEmailChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mb-4">
								<label
									for="reply-to_name"
									class="block text-sm font-medium text-gray-700"
								>
									Reply to name
								</label>
								<input
									type="text"
									name="reply-to"
									id="reply-to"
									value={EmailConfig.replyToName}
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter reply to name"
									required
									onChange={(e) => {
										handleReplyToNameChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mb-4">
								<label for="to" class="block text-sm font-medium text-gray-700">
									To email
								</label>
								<input
									type="email"
									name="to"
									id="to"
									value={props.salesOrder.customer?.email ?? ""}
									// value="<EMAIL>"
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter recipient email"
									required
									onChange={(e) => {
										handleToEmailChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mb-4">
								<label for="to" class="block text-sm font-medium text-gray-700">
									To Name
								</label>
								<input
									type="text"
									name="to_name"
									id="to_name"
									value={props.salesOrder.customer?.name ?? ""}
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter recipient name"
									required
									onChange={(e) => {
										handleToNameChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mb-4">
								<label for="cc" class="block text-sm font-medium text-gray-700">
									CC
								</label>
								<input
									type="text"
									name="cc"
									id="cc"
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter email address"
									onChange={(e) => {
										handleCcChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mb-4">
								<label
									for="bcc"
									class="block text-sm font-medium text-gray-700"
								>
									BCC
								</label>
								<input
									type="text"
									name="bcc"
									id="bcc"
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter email address"
									onChange={(e) => {
										handleBccChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mb-4">
								<label
									for="subject"
									class="block text-sm font-medium text-gray-700"
								>
									Subject
								</label>
								<input
									type="text"
									name="subject"
									id="subject"
									value={`${ucFirst(props.form)}-${
										props.salesOrder.orderNumber
									}`}
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter email subject"
									required
									onChange={(e) => {
										handleSubjectChange(e.currentTarget.value);
									}}
								/>
							</div>
							<div class="mb-4">
								<label
									for="message"
									class="block text-sm font-medium text-gray-700"
								>
									Message
								</label>
								<textarea
									name="message"
									id="message"
									rows={3}
									class="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 shadow-xs focus:border-cyan-500 focus:ring-cyan-500 focus:outline-hidden sm:text-sm"
									placeholder="Enter email message"
									required
									onChange={(e) => {
										handleMessageChange(e.currentTarget.value);
									}}
								>{`Dear ${props.salesOrder.customer?.contactName}, below is your ${props.form} for order number ${props.salesOrder.orderNumber}`}</textarea>
							</div>

							<div class="flex items-center">
								<button
									class="mr-2 ml-auto inline-flex items-center rounded-md border px-3 py-2 text-sm font-medium text-slate-700/50 active:scale-95 sm:py-2"
									onClick={props.onCancel}
								>
									<span class="">Cancel</span>
								</button>
								<button
									type="submit"
									class="inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm text-white active:scale-95 sm:py-2"
									disabled={isSubmitting()}
								>
									<Show
										when={isSubmitting()}
										fallback={<span class="">Send Email</span>}
									>
										<LoadingSpinner
											colorClass="text-white"
											iconClass="h-4 w-4"
										/>
									</Show>
								</button>
							</div>
						</div>
					</form>
				</section>
			}
		>
			<div class="fixed top-0 left-0 z-10 flex h-full w-full items-center justify-center bg-black/50">
				<div class="rounded-sm bg-white px-4 py-2">
					{/* close button right */}
					<div class="flex">
						<button class="mt-1 ml-auto" onClick={props.onCancel}>
							<X class="h-5 w-5" />
						</button>
					</div>
					{/* center icon */}
					<CheckCircle2 class="mx-auto h-20 w-20 text-green-500" />
					<h1 class="mt-5 text-center text-3xl font-bold"> Success </h1>
					<div class="mx-auto mt-3 mb-5 w-2/3 text-center">
						<i class="text-sm font-medium">
							<strong>{props.formName}</strong> form has been sent to{" "}
							<strong>{toEmail()}</strong>
						</i>
					</div>
				</div>
			</div>
		</Show>
	);
}
