import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { fetchPurchaseOrders } from "~/services/http-services/purchase-order-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request }: APIEvent) {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to load purchase orders",
			},
			401,
		);
	}

	const urlObject = new URL(request.url);
	const searchParams = urlObject.searchParams;

	const after = searchParams.get("after");
	const before = searchParams.get("before");
	const keyword = searchParams.get("keyword");

	const response = await fetchPurchaseOrders({
		after: after ?? undefined,
		before: before ?? undefined,
		sortDesc: true,
		keyword: keyword ?? undefined,
	});

	return makeJsonResponse(response);
}
