import { APIEvent } from "@solidjs/start/server";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { findProduct } from "~/services/http-services/product-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    return makeJsonResponse(
      {
        success: false,
        message: "Please login to get product data",
      },
      401,
    );
  }

  const id = params.id;

  const response = await findProduct(id ?? "");

  return makeJsonResponse(response);
}
