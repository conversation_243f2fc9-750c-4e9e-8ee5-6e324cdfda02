module.exports = {
	apps: [{
		name: "multay-one",
		script: "./dist/server.js", // Adjust this path based on your build output
		instances: 1,
		exec_mode: "cluster",

		// Environment configuration
		env: {
			NODE_ENV: "production",
			// Database configuration
			DB_CONNECTION: process.env.DB_CONNECTION,
			DB_HOST: process.env.DB_HOST,
			DB_PORT: process.env.DB_PORT,
			DB_NAME: process.env.DB_NAME,
			DB_USER: process.env.DB_USER,
			DB_PASSWORD: process.env.DB_PASSWORD,
			DB_CHARSET: process.env.DB_CHARSET,
			DB_COLLATION: process.env.DB_COLLATION,

			// Application configuration
			DEPLOYMENT_PLATFORM: process.env.DEPLOYMENT_PLATFORM,
			COOKIE_NAME: process.env.COOKIE_NAME,
			SESSION_SECRET: process.env.SESSION_SECRET,

			// inFlow API configuration
			INFLOW_COMPANY_ID: process.env.INFLOW_COMPANY_ID,
			INFLOW_API_KEY: process.env.INFLOW_API_KEY,
			INFLOW_INVENTORY_CATEGORY_ID: process.env.INFLOW_INVENTORY_CATEGORY_ID,
			INFLOW_SALES_PRODUCT_CATEGORY_ID: process.env.INFLOW_SALES_PRODUCT_CATEGORY_ID,
			INFLOW_INCOME_CATEGORY_ID: process.env.INFLOW_INCOME_CATEGORY_ID,
			INFLOW_DEFAULT_PRICING_SCHEME_ID: process.env.INFLOW_DEFAULT_PRICING_SCHEME_ID,
			INFLOW_DEFAULT_INVOICE_PAYMENT_TERMS_ID: process.env.INFLOW_DEFAULT_INVOICE_PAYMENT_TERMS_ID,

			// Supabase configuration
			SUPABASE_PROJECT_URL: process.env.SUPABASE_PROJECT_URL,
			SUPABASE_PROJECT_PUBLIC_KEY: process.env.SUPABASE_PROJECT_PUBLIC_KEY,
			SUPABASE_PROJECT_SECRET_KEY: process.env.SUPABASE_PROJECT_SECRET_KEY,

			// Mailjet configuration
			MAILJET_APIKEY_PUBLIC: process.env.MAILJET_APIKEY_PUBLIC,
			MAILJET_APIKEY_PRIVATE: process.env.MAILJET_APIKEY_PRIVATE,

			// Multay One API configuration
			MULTAY_ONE_ACCESS_KEY: process.env.MULTAY_ONE_ACCESS_KEY,
			MULTAY_ONE_SECRET_KEY: process.env.MULTAY_ONE_SECRET_KEY
		},

		// PM2 configuration for production
		watch: false,
		ignore_watch: ["node_modules", "logs"],

		// Logging configuration
		log_file: "./logs/combined.log",
		out_file: "./logs/out.log",
		error_file: "./logs/error.log",
		log_date_format: "YYYY-MM-DD HH:mm:ss Z",

		// Process management
		max_memory_restart: "1G",
		restart_delay: 4000,

		// Environment variable refresh
		kill_timeout: 5000,
		listen_timeout: 8000,

		// This ensures environment variables are read fresh on restart
		env_production: {
			NODE_ENV: "production"
		}
	}]
}
