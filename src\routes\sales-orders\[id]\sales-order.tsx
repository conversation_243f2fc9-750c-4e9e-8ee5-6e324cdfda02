import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { Show } from "solid-js";
import BankAndAccountDetail from "~/components/BankAndAccountDetail";
import SalesOrderHeader from "~/components/heading/SalesOrderHeader";
import SalesOrderTable from "~/components/tables/SalesOrderTable";
import { inflowConfig } from "~/configs/server-config";
import { CustomProductData } from "~/types/dto";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { findSomeCustomProductDataList } from "~/services/http-services/custom-product-data-service";
import { findSalesOrder } from "~/services/http-services/sales-order-service";

const getPageData = query(async (salesOrderId: string) => {
	"use server";

	const isLoggedIn = await isAuthenticated();

	if (!isLoggedIn) {
		throw redirect("/login/");
	}

	// console.log("salesOrderId:", salesOrderId);

	const salesOrderResponse = await findSalesOrder(salesOrderId);

	const salesOrder = salesOrderResponse.data;
	const customProductDataCollection: Record<string, CustomProductData> = {};

	if (salesOrderResponse.success) {
		const productLines = salesOrderResponse.data?.lines ?? [];
		const productIds: string[] = [];

		for (const line of productLines) {
			productIds.push(line.productId);
		}

		const id = salesOrderResponse.data?.customerId ?? "";
		function findSomeCustomProductDataListResponse() {
			if (id === "731e777d-8ef1-4d13-a77c-54a8842dcdc7") {
				return findSomeCustomProductDataList({
					customerId: "1caa0847-b862-44a6-ab7f-a605ea8b67da",
					productIds: productIds,
				});
			} else {
				return findSomeCustomProductDataList({
					customerId: id,
					productIds: productIds,
				});
			}
		}

		const customProductDataListResponse =
			await findSomeCustomProductDataListResponse();

		if (customProductDataListResponse.success) {
			const customProductDataList = customProductDataListResponse.data ?? [];

			for (const customProductData of customProductDataList) {
				customProductDataCollection[customProductData.productId] =
					customProductData;
			}
		}
	}

	return {
		salesOrder: salesOrder,
		customProductDataCollection: customProductDataCollection,
		incomeCategoryId: inflowConfig().incomeCategoryId,
	};
}, "salesOrderPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function SalesOrderPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	return (
		<div class="w-[1280px] px-4 py-12 lg:w-auto xl:px-20 print:w-auto print:px-0 print:py-0">
			<Show
				when={
					pageData() &&
					pageData()?.salesOrder &&
					pageData()?.customProductDataCollection
				}
				fallback="Loading data..."
			>
				<SalesOrderHeader salesOrder={pageData()?.salesOrder!} />
				<SalesOrderTable
					salesOrder={pageData()?.salesOrder!}
					customProductDataCollection={pageData()?.customProductDataCollection!}
					incomeCategoryId={pageData()?.incomeCategoryId ?? ""}
				/>
			</Show>
			<BankAndAccountDetail />
		</div>
	);
}
