import {
	A,
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { fetchCustomFields } from "~/services/http-services/custom-field-service";
import { findProduct } from "~/services/http-services/product-service";
import { findPricingScheme } from "~/services/http-services/pricing-scheme-service";
import { formatQuantity } from "~/utils/formatting-util";
import { getAuthData } from "~/services/auth-services/supabase-auth-service";

const getPageData = query(async (productId: string) => {
	"use server";

	const authDataResponse = await getAuthData();

	if (!authDataResponse.success) {
		throw redirect("/login/");
	}

	const productResponse = await findProduct(productId);

	let pricing = null;

	if (productResponse.success) {
		const pricingResponse = await findPricingScheme(
			productResponse.data?.prices[0].pricingSchemeId ?? "-",
		);

		if (pricingResponse.success) {
			pricing = pricingResponse.data;
		}
	}

	const customFields = await fetchCustomFields();
	const product = productResponse.data;

	return {
		product,
		customFields,
		pricing,
	};
}, "productDetailPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function productDetailPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	return (
		<div class="">
			<header class="fixed w-full bg-white px-[1rem] pt-[1rem] pb-[1rem] shadow-md md:px-0 md:pb-0">
				<div class="md:container md:flex">
					<div>
						<h4 class="text-2xl font-bold text-sky-700">Detail products</h4>
						<nav class="mt-1 mb-1 flex items-center" aria-label="Breadcrumb">
							<ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
								<li class="inline-flex items-center">
									<A
										href="/"
										class="hover:text-primary inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
									>
										Dashboard
									</A>
								</li>
								<li>
									<div class="flex items-center">
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>
										<A
											href="/products"
											class="hover:text-primary mx-2 inline-flex items-center text-sm text-gray-700 dark:text-gray-400 dark:hover:text-white"
										>
											products
										</A>
										<svg
											class="h-2.5 w-2.5 text-gray-400 rtl:rotate-180"
											aria-hidden="true"
											xmlns="http://www.w3.org/2000/svg"
											fill="none"
											viewBox="0 0 6 10"
										>
											<path
												stroke="currentColor"
												stroke-linecap="round"
												stroke-linejoin="round"
												stroke-width="2"
												d="m1 9 4-4-4-4"
											/>
										</svg>
										<span class="ms-1 text-sm text-gray-700 md:ms-2 dark:text-gray-400 dark:hover:text-white">
											Detail products
										</span>
									</div>
								</li>
							</ol>
						</nav>
					</div>
				</div>
			</header>
			<div class="container items-center pt-24 text-sm md:flex">
				<img
					src={pageData()?.product?.images![0]!.largeUrl}
					alt=""
					class="image-fit mx-auto h-96 w-96 rounded-md border shadow-md md:mx-0"
				/>
				<div class="mt-4 ml-5 md:mt-0">
					<p class="text-3xl font-bold text-sky-700 md:text-4xl">
						{pageData()?.product?.name}
					</p>
					<div class="mt-2 flex border-b pb-2">
						<div class="text-stone-500">
							<p>SKU </p>
							<p>category </p>
							<p>barcode </p>
						</div>
						<div class="ml-2 font-semibold">
							<p>: {pageData()?.product?.sku}</p>
							<p>: {pageData()?.product?.category.name}</p>
							<p>: {pageData()?.product?.barcode}</p>
						</div>
					</div>
					<div class="mt-2 grid grid-cols-2 border-b pb-2">
						<div class="flex">
							<div class="text-stone-500">
								<p>
									{pageData()?.customFields?.productCustomFieldLabels.custom1}
								</p>
								<p>
									{pageData()?.customFields?.productCustomFieldLabels.custom2}
								</p>
								<p>
									{pageData()?.customFields?.productCustomFieldLabels.custom3}
								</p>
								<p>
									{pageData()?.customFields?.productCustomFieldLabels.custom4}
								</p>
							</div>
							<div class="ml-2 font-semibold">
								<p>: {pageData()?.product?.customFields.custom1 || "-"}</p>
								<p>: {pageData()?.product?.customFields.custom2 || "-"}</p>
								<p>: {pageData()?.product?.customFields.custom3 || "-"}</p>
								<p>: {pageData()?.product?.customFields.custom4 || "-"}</p>
							</div>
						</div>
						<div class="flex">
							<div class="text-stone-500">
								<p>
									{pageData()?.customFields?.productCustomFieldLabels.custom5}
								</p>
								<p>
									{pageData()?.customFields?.productCustomFieldLabels.custom6}
								</p>
								<p>
									{pageData()?.customFields?.productCustomFieldLabels.custom7}
								</p>
							</div>
							<div class="ml-2 font-semibold">
								<p>: {pageData()?.product?.customFields.custom5 || "-"}</p>
								<p>: {pageData()?.product?.customFields.custom6 || "-"}</p>
								<p>: {pageData()?.product?.customFields.custom7 || "-"}</p>
							</div>
						</div>
					</div>
					<div class="mt-2 grid grid-cols-2">
						<div class="text-stone-500">
							<p>Quantity on hand</p>
							<p>Standar quantity</p>
							<p>Uom quantity</p>
							<p>Remarks</p>
							<p>{pageData()?.product?.prices[0].pricingScheme?.name}</p>
							<p>{pageData()?.product?.prices[0].priceType}</p>
							<p>{pageData()?.pricing?.currency.name}</p>
							<p>{pageData()?.product?.prices[0].productPriceId}</p>
						</div>
						<div class="font-semibold">
							{/* <p>{routingData()?.product?.totalQuantityOnHand}</p> */}
							<p>
								{formatQuantity(pageData()?.product?.totalQuantityOnHand ?? 0)}
							</p>
							<p>
								{pageData()?.product?.purchasingUom?.conversionRatio
									?.standardQuantity || "-"}
							</p>
							<p>
								{pageData()?.product?.purchasingUom?.conversionRatio
									?.uomQuantity || "-"}
							</p>
							<p>{pageData()?.product?.remarks || "-"}</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
