import {
	RouteDefinition,
	RouteSectionProps,
	createAsync,
	query,
	redirect,
} from "@solidjs/router";
import { Show } from "solid-js";
import { getRequestURL } from "vinxi/http";
import JobOrderHeader from "~/components/heading/JobOrderHeader";
import JobOrderTable from "~/components/tables/JobOrderTable";
import { CustomProductData } from "~/types/dto";
import { AppUrlData } from "~/types/misc";
import { getAuthData } from "~/services/auth-services/supabase-auth-service";
import { findSomeCustomProductDataList } from "~/services/http-services/custom-product-data-service";
import {
	fetchJobOrderCustomColumns,
	fetchJobOrderProductRemarks,
} from "~/services/http-services/job-order-service";
import { findSalesOrder } from "~/services/http-services/sales-order-service";

const getPageData = query(async (salesOrderId: string) => {
	"use server";

	const authDataResponse = await getAuthData();

	if (!authDataResponse.success) {
		throw redirect("/login/");
	}

	const urlObject = getRequestURL();
	const baseUrl = urlObject.origin;

	const urls: AppUrlData = {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		currentUrl: urlObject.href,
	};

	const salesOrderResponse = await findSalesOrder(salesOrderId);

	const salesOrder = salesOrderResponse.data;
	const customProductDataCollection: Record<string, CustomProductData> = {};

	const productLines = salesOrderResponse.data?.lines ?? [];
	const productIds: string[] = [];

	for (const line of productLines) {
		productIds.push(line.productId);
	}

	const remarksListResponse = await fetchJobOrderProductRemarks({
		salesOrderId: salesOrderId,
		productIds: productIds,
	});

	if (salesOrderResponse.success) {
		const productLines = salesOrderResponse.data?.lines ?? [];
		const productIds: string[] = [];

		for (const line of productLines) {
			productIds.push(line.productId);
		}

		const customProductDataListResponse = await findSomeCustomProductDataList({
			customerId: salesOrderResponse.data?.customerId ?? "",
			productIds: productIds,
		});

		if (customProductDataListResponse.success) {
			const customProductDataList = customProductDataListResponse.data ?? [];

			for (const customProductData of customProductDataList) {
				customProductDataCollection[customProductData.productId] =
					customProductData;
			}
		}
	}

	const jobOrderCustomColumnListResponse = await fetchJobOrderCustomColumns({
		salesOrderId: salesOrderId,
	});

	const customColumn =
		jobOrderCustomColumnListResponse.success &&
		jobOrderCustomColumnListResponse.data &&
		jobOrderCustomColumnListResponse.data.length
			? jobOrderCustomColumnListResponse.data[0]
			: undefined;

	return {
		urls: urls,
		profile: authDataResponse?.data,
		salesOrder: salesOrder,
		customProductDataCollection: customProductDataCollection,
		remarksList: remarksListResponse.data ?? [],
		customColumn: customColumn,
	};
}, "jobOrderPageData");

export const route = {
	preload: ({ params }) => getPageData(params.id),
} satisfies RouteDefinition;

export default function JobOrderPage(props: RouteSectionProps) {
	const pageData = createAsync(() => getPageData(props.params.id), {
		deferStream: true,
	});

	return (
		<div class="w-[1280px] px-4 py-12 lg:w-auto xl:px-20 print:w-auto print:px-0 print:py-0">
			<Show
				when={pageData() && pageData()?.salesOrder && pageData()?.remarksList}
				fallback="Loading data..."
			>
				<JobOrderHeader salesOrder={pageData()?.salesOrder!} />
				<JobOrderTable
					baseApiUrl={pageData()?.urls.baseApiUrl ?? "/api"}
					salesOrder={pageData()?.salesOrder!}
					customProductDataCollection={pageData()?.customProductDataCollection!}
					remarksList={pageData()?.remarksList ?? []}
					customColumn={pageData()?.customColumn}
					currentUser={pageData()?.profile}
				/>
			</Show>
		</div>
	);
}
