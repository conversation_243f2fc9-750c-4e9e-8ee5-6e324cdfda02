import slugify from "@sindresorhus/slugify";

export function ucFirst(string: string): string {
	return string.charAt(0).toUpperCase() + string.slice(1);
}

export function toPricingSchemeSlug(name: string): string {
	return slugify(name.trim().replace(/\s\$/g, ""));
}

export function slugToText(slug: string): string {
	if (!slug || typeof slug !== "string") {
		return "";
	}

	return (
		slug
			// Replace hyphens and underscores with spaces
			.replace(/[-_]/g, " ")
			// Remove any remaining non-alphanumeric characters except spaces
			.replace(/[^a-zA-Z0-9\s]/g, "")
			// Replace multiple consecutive spaces with single space
			.replace(/\s+/g, " ")
			// Trim whitespace from start and end
			.trim()
	);
}
