export function isNumeric(val: unknown): boolean {
	return typeof val === "number"
		? Number.isFinite(val)
		: typeof val === "string" && val.trim() !== "" && !isNaN(Number(val));
}

export function numericToNumber(value: string): number {
	return parseFloat(value.replace(/,/g, ""));
}

export function toProperNumberType(char: string | number): number {
	if (!char) {
		return 0;
	}

	if (typeof char !== "string") {
		char = String(char);
	}

	const numValue = Number(char);
	const numStr = String(numValue);

	return numStr.includes(".") ? numValue : Math.trunc(numValue);
}
