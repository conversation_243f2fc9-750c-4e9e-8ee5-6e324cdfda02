import {
	action,
	createAsync,
	query,
	redirect,
	RouteDefinition,
	useAction,
	useSubmission,
} from "@solidjs/router";
import { Loader2 } from "lucide-solid";
import { createSignal, Show } from "solid-js";
import {
	dbCollectCategories,
	dbMakeStructuredCategories,
} from "~/services/db-services/category-db-service";
import { dbCollectCollections } from "~/services/db-services/collection-db-service";
import { dbCollectCustomers } from "~/services/db-services/customer-db-service";
import { dbCollectPricingSchemes } from "~/services/db-services/pricing-scheme-db-service";
import { dbCollectSalesProducts } from "~/services/db-services/product-db-service";
import { getAuthData } from "~/services/auth-services/supabase-auth-service";
import { SetupDataResult, SimpleResponse } from "~/types/response";
import { delay } from "~/utils/time-util";

// Define the action types
type ActionType =
	| "collect-categories"
	| "make-structured-categories"
	| "collect-collections"
	| "collect-sales-products"
	| "collect-customers"
	| "collect-pricing-schemes";

// Define a type map that explicitly links actions to return types
interface ActionReturnTypeMap {
	"collect-categories": SetupDataResult;
	"make-structured-categories": SimpleResponse;
	"collect-collections": SimpleResponse;
	"collect-sales-products": SetupDataResult;
	"collect-customers": SetupDataResult;
	"collect-pricing-schemes": SimpleResponse;
}

async function serverSetupData(
	task: ActionType,
	args?: { after?: string },
): Promise<ActionReturnTypeMap[ActionType]> {
	"use server";

	if (task === "collect-categories") {
		return await dbCollectCategories({
			after: args?.after,
		});
	}

	if (task === "make-structured-categories") {
		return await dbMakeStructuredCategories();
	}

	if (task === "collect-collections") {
		return await dbCollectCollections();
	}

	if (task === "collect-sales-products") {
		return await dbCollectSalesProducts({
			after: args?.after,
		});
	}

	if (task === "collect-customers") {
		return await dbCollectCustomers({
			after: args?.after,
		});
	}

	if (task === "collect-pricing-schemes") {
		return await dbCollectPricingSchemes({
			after: args?.after,
		});
	}

	return {
		success: false,
		message: "Action not implemented",
	};
}

const setupDataAction = action(serverSetupData, "setup-data-action");

const getPageData = query(async () => {
	"use server";

	const authDataResponse = await getAuthData();

	if (!authDataResponse.success || !authDataResponse.data) {
		throw redirect("/login/");
	}

	if (
		authDataResponse.data.role !== "developer" &&
		authDataResponse.data.role !== "administrator"
	) {
		throw redirect("/");
	}

	return authDataResponse.data;
}, "setupPageData");

export const route = {
	preload: () => getPageData(),
} satisfies RouteDefinition;

export default function SetupPage() {
	const profile = createAsync(() => getPageData(), { deferStream: true });

	const menuLinkBlockClassName =
		"mb-6 flex h-20 transform items-center rounded-xl bg-[#F3FBF6] p-6 transition-all duration-300 ease-in-out hover:bg-green-100 active:scale-90 sm:mb-0";

	const menuTextClassName = "block font-bold text-green-600";

	const [showOverlay, setShowOverlay] = createSignal(false);

	const setupData = useAction(setupDataAction);
	const settingUpData = useSubmission(setupDataAction);

	async function recursivelySetupData(
		task: ActionType,
		args?: { after?: string },
	): Promise<ActionReturnTypeMap[ActionType]> {
		if (
			task === "collect-categories" ||
			task === "collect-sales-products" ||
			task === "collect-customers" ||
			task === "collect-pricing-schemes"
		) {
			const result = await setupData(task, args);

			if (!result.success) return result;

			if ("meta" in result && result.meta.hasMore && result.meta.lastId) {
				// Wait for 2 seconds before continue processing the recursive.
				await delay(2000);

				const recursiveResult = await recursivelySetupData(task, {
					after: result.meta.lastId,
				});

				return recursiveResult;
			}

			return result;
		}

		return await setupData(task, args);
	}

	async function handleButtonClick(task: ActionType) {
		setShowOverlay(true);
		await recursivelySetupData(task);
		setShowOverlay(false);
	}

	return (
		<>
			<main
				class={`relative flex min-h-screen flex-wrap items-center justify-center py-6 text-center`}
			>
				<section class="mx-auto max-w-6xl items-center px-[1rem]">
					<div class="relative mb-3 lg:px-32">
						<div class="sm:grid sm:grid-cols-3 sm:gap-6">
							<button
								type="button"
								class={`${menuLinkBlockClassName}`}
								onClick={() => handleButtonClick("collect-categories")}
							>
								<span class="mx-auto block">
									<span class={`${menuTextClassName}`}>Collect Categories</span>
								</span>
							</button>

							<button
								type="button"
								class={`${menuLinkBlockClassName}`}
								onClick={() => handleButtonClick("make-structured-categories")}
							>
								<span class="mx-auto block">
									<span class={`${menuTextClassName}`}>
										Make Nested Categories
									</span>
								</span>
							</button>

							<button
								type="button"
								class={`${menuLinkBlockClassName}`}
								onClick={() => handleButtonClick("collect-collections")}
							>
								<span class="mx-auto block">
									<span class={`${menuTextClassName}`}>
										Collect Collections
									</span>
								</span>
							</button>

							<button
								type="button"
								class={`${menuLinkBlockClassName}`}
								onClick={() => handleButtonClick("collect-sales-products")}
							>
								<span class="mx-auto block">
									<span class={`${menuTextClassName}`}>
										Collect Sales Products
									</span>
								</span>
							</button>

							<button
								type="button"
								class={`${menuLinkBlockClassName}`}
								onClick={() => handleButtonClick("collect-customers")}
							>
								<span class="mx-auto block">
									<span class={`${menuTextClassName}`}>Collect Customers</span>
								</span>
							</button>

							<button
								type="button"
								class={`${menuLinkBlockClassName}`}
								onClick={() => handleButtonClick("collect-pricing-schemes")}
							>
								<span class="mx-auto block">
									<span class={`${menuTextClassName}`}>
										Collect Pricing Schemes
									</span>
								</span>
							</button>
						</div>
					</div>
				</section>
			</main>

			<Show when={showOverlay()}>
				<div
					class={`overlay visible fixed inset-0 z-50 flex items-center justify-center bg-black/50 text-white opacity-100 backdrop-blur-xs transition-all duration-300 ease-in-out`}
				>
					<Loader2 size={48} class={`mx-auto animate-spin`} />
				</div>
			</Show>
		</>
	);
}
