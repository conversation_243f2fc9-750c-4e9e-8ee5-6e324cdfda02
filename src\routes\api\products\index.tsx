import { APIEvent } from "@solidjs/start/server";
import { URL } from "url";
import { isAuthenticated } from "~/services/auth-services/supabase-auth-service";
import { fetchProducts } from "~/services/http-services/product-service";
import { makeJsonResponse } from "~/utils/http-util";

export async function GET({ request, params }: APIEvent) {
  const isLoggedIn = await isAuthenticated();

  if (!isLoggedIn) {
    return makeJsonResponse(
      {
        success: false,
        message: "Please login to load products",
      },
      401,
    );
  }

  const urlObject = new URL(request.url);
  const searchParams = urlObject.searchParams;

  const after = searchParams.get("after");
  const before = searchParams.get("before");
  const shortDesc = searchParams.get("shortDesc");
  const keyword = searchParams.get("keyword");

  const response = await fetchProducts({
    after: after ?? undefined,
    before: before ?? undefined,
    sortDesc: shortDesc === "true",
    keyword: keyword ?? undefined,
  });

  return makeJsonResponse(response);
}
