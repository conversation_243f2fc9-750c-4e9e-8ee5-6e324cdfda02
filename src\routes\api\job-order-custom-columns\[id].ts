import { APIEvent } from "@solidjs/start/server";
import { allowedEmailsToManageJobOrder } from "~/configs/app-config";
import { getAuthData } from "~/services/auth-services/supabase-auth-service";
import {
	deleteJobOrderCustomColumn,
	updateJobOrderCustomColumn,
} from "~/services/http-services/job-order-service";
import { makeJsonResponse } from "~/utils/http-util";
import { maybeParseJson } from "~/utils/json-utils";

export async function DELETE({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to delete job order product remarks",
			},
			401,
		);
	}

	if (!allowedEmailsToManageJobOrder.includes(authDataResponse.data.email)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to delete job order product remarks",
			},
			401,
		);
	}

	const id = params.id;

	const response = await deleteJobOrderCustomColumn(Number(id));

	return makeJsonResponse(response);
}

export async function PUT({ request, params }: APIEvent) {
	const authDataResponse = await getAuthData();

	if (!authDataResponse || !authDataResponse.data) {
		return makeJsonResponse(
			{
				success: false,
				message: "Please login to update job order product remarks",
			},
			401,
		);
	}

	if (!allowedEmailsToManageJobOrder.includes(authDataResponse.data.email)) {
		return makeJsonResponse(
			{
				success: false,
				message: "You are not authorized to update job order product remarks",
			},
			401,
		);
	}

	// Get PUT data.
	const body = await request.json();

	// This is the team member <NAME_EMAIL> member in inFlow.
	const teamMemberId = "9e31e1dd-a3ba-4f46-87f1-e155d14f17a5";
	const id = params.id;
	const salesOrderId = body.salesOrderId;
	const name = body.name;
	const values = maybeParseJson(body.values);

	const response = await updateJobOrderCustomColumn({
		id: Number(id),
		teamMemberId: teamMemberId,
		salesOrderId: salesOrderId,
		name: name,
		values: values,
	});

	return makeJsonResponse(response);
}
