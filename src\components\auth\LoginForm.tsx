import { useSubmission, action, redirect } from "@solidjs/router";
import { <PERSON>, EyeOff, Loader2 } from "lucide-solid";
import { For, Show, createSignal } from "solid-js";
import { login } from "~/services/auth-services/supabase-auth-service";
import { getInputFieldClassName } from "~/utils/classname-util";
import {
	valibotIssueMessage,
	valibotIssuesHaveField,
} from "~/utils/validation-util";

const loginAction = action(async (formData: FormData) => {
	"use server";

	const result = await login(formData);

	if (!result.success || !result.data) {
		return result;
	}

	throw redirect("/");
}, "login-action");

export default function LoginForm() {
	function hasError(fieldName: string): boolean {
		if (!loggingIn.result || loggingIn.result.success) {
			return false;
		}

		const issues = loggingIn.result.issues;

		return issues ? valibotIssuesHaveField({ fieldName, issues }) : false;
	}

	function getErrorMessage(fieldName: string): string {
		if (!loggingIn.result || loggingIn.result.success) {
			return "";
		}

		const issues = loggingIn.result.issues;

		return issues ? valibotIssueMessage({ fieldName, issues }) : "";
	}

	const [showPassword, setShowPassword] = createSignal(false);
	const loggingIn = useSubmission(loginAction);

	return (
		<main class="mx-auto p-[1rem] text-center">
			<div class="mx-auto max-w-3xl py-32 text-gray-800">
				<h1 class="text-5xl font-bold sm:text-[88px] sm:leading-[80px]">
					Internal app for Multay.
				</h1>
				<p class="mt-6 text-xs font-medium text-black/50 sm:mt-10 sm:text-lg">
					Complementary app to ease Multay's sales forms creation process.
					<br />
					Please login with your internal account.
				</p>

				<form
					action={loginAction}
					method="post"
					class="mx-auto mt-10 max-w-[300px] rounded-md bg-white p-5 shadow-md sm:mt-14 sm:max-w-[500px] sm:rounded-lg sm:p-10 sm:shadow-lg"
				>
					<div class="text-left">
						<label class="block font-semibold" for="email-field">
							Email
						</label>
						<input
							type="email"
							name="email"
							id="email-field"
							placeholder="Enter your email"
							required
							class={getInputFieldClassName({
								submissionStatus: hasError("email") ? "error" : "default",
							})}
						/>
						<Show when={hasError("email")}>
							<p role="alert" class="mt-1 font-light text-red-500">
								{getErrorMessage("email")}
							</p>
						</Show>
					</div>
					<div class="mt-7 text-left">
						<label class="block font-semibold" for="password-field">
							Password
						</label>
						<div class="relative">
							<input
								type={showPassword() ? "text" : "password"}
								name="password"
								id="password-field"
								placeholder="Enter your password"
								class={getInputFieldClassName({
									submissionStatus: hasError("password") ? "error" : "default",
								})}
							/>
							<button
								type="button"
								onClick={() => setShowPassword(!showPassword())}
								class="absolute top-0 right-0 flex h-full w-10 items-center justify-center"
							>
								<Show when={!showPassword()}>
									<Eye class="h-4 w-4 text-gray-500" />
								</Show>
								<Show when={showPassword()}>
									<EyeOff class="h-4 w-4 text-gray-500" />
								</Show>
							</button>
						</div>
						<Show when={hasError("password")}>
							<p role="alert" class="mt-1 font-light text-red-500">
								{getErrorMessage("password")}
							</p>
						</Show>
					</div>
					<div class="mt-5 text-right">
						{/* <A
              href="/forget-pasword"
              class="text-xs font-semibold text-blue-500"
            >
              Forget Password?
            </A> */}
					</div>
					<button
						type="submit"
						class="relative mt-10 block w-full rounded-md bg-blue-500 p-3 text-white active:opacity-50"
					>
						<span class={loggingIn.pending ? "opacity-0" : ""}>Login</span>
						<Show when={loggingIn.pending}>
							<div class="absolute top-0 left-0 inline-flex h-full w-full items-center justify-center text-white">
								<Loader2 class={`mx-auto h-5 w-5 animate-spin`} />
							</div>
						</Show>
					</button>

					<Show when={loggingIn.result}>
						<div
							class={`mt-8 rounded-sm border p-2 ${loggingIn.result?.success ? "border-green-500 text-green-500" : "border-red-500 text-red-500"}`}
							role="alert"
						>
							<Show
								when={!loggingIn.result?.success}
								fallback={loggingIn.result?.message}
							>
								<Show
									when={loggingIn.result?.issues}
									fallback={loggingIn.result?.message}
								>
									<ul>
										<For each={loggingIn.result?.issues ?? []}>
											{(issue) => <li class="mt-1">{issue.message}</li>}
										</For>
									</ul>
								</Show>
							</Show>
						</div>
					</Show>
				</form>
			</div>
		</main>
	);
}
