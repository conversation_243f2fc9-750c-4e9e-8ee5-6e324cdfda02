import dayjs from "dayjs";
import { ChevronRight } from "lucide-solid";
import { SalesOrderData } from "~/types/dto";
import { formatCurrency } from "~/utils/formatting-util";
import {
	MobileColon,
	FulfilledTag,
	PriceTag,
	QuoteTag,
	UnFullfiledTag,
} from "../tables/tags";
import { numericToNumber } from "~/utils/number-util";

export interface SalesOrderDataRowProps {
	salesOrder: SalesOrderData;
	onViewFormsButtonClick?: (salesOrder: SalesOrderData) => void;
}

export default function SalesOrderDataRow(props: SalesOrderDataRowProps) {
	function formatDate(date: string): string {
		const dateObj = dayjs(date);
		return dateObj.format("MMM DD, YYYY");
	}

	function handleViewFormsButtonClick() {
		if (props.onViewFormsButtonClick) {
			props.onViewFormsButtonClick(props.salesOrder);
		}
	}

	function formatPrice(amount: string | number): string {
		return formatCurrency({
			amount: amount,
			symbol: props.salesOrder.currency?.symbol,
			thousandsSeparator: props.salesOrder.currency?.thousandsSeparator,
			decimalSeparator: props.salesOrder.currency?.decimalSeparator,
			decimalDigits: props.salesOrder.currency?.decimalPlaces,
		});
	}

	const columnClassName = "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-1 ";
	const mobileLabelClassName = "text-gray-500 text-ellipsis md:hidden";
	const dataValueClassName = "text-gray-800 text-ellipsis sm:col-span-2";

	function styleIsInvoiced() {
		if (props.salesOrder.isCompleted) {
			return "border-yellow-600 text-white bg-yellow-600";
		} else if (props.salesOrder.isCancelled) {
			// bg is gradient form white to orange to white
			return "border-yellow-600 text-white bg-linear-to-r from-yellow-50 via-yellow-600/25 to-white text-yellow-600"; // ini maas???s
		} else {
			return "border-gray-700 text-gray-700 font-semibold";
		}
	}

	function salesRep() {
		if (props.salesOrder.salesRepTeamMember?.name) {
			return (
				<span class="mr-2 ml-auto flex h-6 w-6 items-center justify-center rounded-full bg-yellow-700 text-xs font-semibold text-white">
					{props.salesOrder.salesRepTeamMember?.name.charAt(0).toUpperCase()}
				</span>
			);
		} else {
			return <></>;
		}
	}

	const balanceStr = props.salesOrder.balance;
	const balance = numericToNumber(balanceStr);

	const totalPriceStr = props.salesOrder.total;
	const totalPrice = numericToNumber(totalPriceStr);

	const percentage = (balance / totalPrice) * 100;

	return (
		<>
			<div
				onClick={handleViewFormsButtonClick}
				// class="flex w-full border-b px-[1rem] py-2 text-left md:hidden"
				class={
					props.salesOrder.isCancelled
						? "hidden"
						: "flex w-full border-b px-[1rem] py-2 text-left md:hidden"
				}
			>
				<div>
					<p class="font-bold">{props.salesOrder.orderNumber}</p>
					<p class="text-xs font-light">
						{props.salesOrder.customer?.name ?? props.salesOrder.contactName}
					</p>
					<div class="mt-1 flex items-center">
						<span>{props.salesOrder.isCompleted ? <FulfilledTag /> : ""}</span>
						<span>
							{props.salesOrder.inventoryStatus === "unfulfilled" ? (
								<UnFullfiledTag />
							) : (
								""
							)}
						</span>
						<span>{props.salesOrder.isQuote ? <QuoteTag /> : ""}</span>
					</div>
				</div>
				<div class="mt-auto ml-auto">
					<div
						class={
							styleIsInvoiced() +
							" rounded-full border border-solid px-2 text-xs"
						}
					>
						{formatPrice(totalPrice)}
					</div>
				</div>
				<div>
					<ChevronRight class="ml-2" />
				</div>
			</div>

			<div
				onClick={handleViewFormsButtonClick}
				// class="container hidden  items-center border-b border-green-100 py-2 md:flex"
				class={
					props.salesOrder.isCancelled
						? "hidden"
						: "container hidden cursor-pointer items-center border-b border-green-100 py-2 md:flex"
				}
			>
				<div class={columnClassName + " text-left md:w-[20%]"}>
					<span class={mobileLabelClassName}>Order number</span>
					<div class="flex flex-wrap items-center">
						<p class={dataValueClassName + " text-sm font-bold"}>
							{props.salesOrder.orderNumber}
						</p>
						<span>{props.salesOrder.isCompleted ? <FulfilledTag /> : ""}</span>
						{/* totalPaymentPercentageColor */}

						<span>
							{props.salesOrder.inventoryStatus === "unfulfilled" ? (
								<UnFullfiledTag />
							) : (
								""
							)}
						</span>
						<span>{props.salesOrder.isQuote ? <QuoteTag /> : ""}</span>
					</div>

					{/* {props.salesOrder.customer?.defaultSalesRepTeamMember.name} */}
				</div>
				<div class={columnClassName + " text-left md:w-[22%]"}>
					<span class={mobileLabelClassName}>Customer</span>
					<div class="flex">
						<MobileColon />
						<span class={dataValueClassName + " w-4/5 text-sm"}>
							{props.salesOrder.customer?.name ?? props.salesOrder.contactName}
						</span>
						{salesRep()}
					</div>
					{/* {salesRepCustomer()} */}
				</div>
				<div class={columnClassName + " text-left md:w-[10%]"}>
					<span class={mobileLabelClassName}>Order date</span>
					<div class="flex">
						<MobileColon />
						<span class={dataValueClassName + "text-xs"}>
							{formatDate(props.salesOrder.orderDate)}
						</span>
					</div>
				</div>
				<div class={columnClassName + " text-left md:w-[10%]"}>
					<span class={mobileLabelClassName}>Requested ship date</span>
					<div class="flex">
						<MobileColon />
						<span class={dataValueClassName + "text-xs"}>
							{props.salesOrder.requestedShipDate
								? formatDate(props.salesOrder.requestedShipDate)
								: "-"}
						</span>
					</div>
				</div>
				<div class={columnClassName + " text-left md:w-[20%]"}>
					<span class={mobileLabelClassName}>Shipping address</span>
					<div class="flex">
						<MobileColon />
						<span class={dataValueClassName + "text-xs"}>
							{props.salesOrder.shippingAddress.address1}
						</span>
					</div>
				</div>
				<div class={columnClassName + " text-right md:w-[9%]"}>
					<span class={mobileLabelClassName}>Total</span>
					<div class="flex">
						<PriceTag
							amount={totalPrice}
							symbol={props.salesOrder.currency?.symbol}
							percentage={percentage}
							thousandsSeparator={props.salesOrder.currency?.thousandsSeparator}
							decimalSeparator={props.salesOrder.currency?.decimalSeparator}
							decimalDigits={props.salesOrder.currency?.decimalPlaces}
						/>
					</div>
				</div>
				<div class={columnClassName + " text-right text-xs md:w-[9%]"}>
					<span class={mobileLabelClassName}>Balance</span>
					<div class="flex">
						<MobileColon />
						<span class={dataValueClassName + " ml-auto px-3 font-bold"}>
							{props.salesOrder.balance ? formatPrice(balance) : "-"}
						</span>
					</div>
				</div>
			</div>
		</>
	);
}
